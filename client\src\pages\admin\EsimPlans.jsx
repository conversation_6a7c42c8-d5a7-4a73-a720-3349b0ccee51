import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import api from '@/lib/axios';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, Search, Pencil, Eye, Trash, RefreshCw, Download, Upload, FileSpreadsheet } from 'lucide-react';
import { io } from 'socket.io-client';
import * as XLSX from 'xlsx';
import { formatCapacityFromKB, convertCNYToUSD, formatPrice } from '@/lib/utils';

export default function EsimPlans() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState(() => {
    const savedPlans = sessionStorage.getItem('adminEsimPlans');
    return savedPlans ? JSON.parse(savedPlans) : [];
  });
  const [searchQuery, setSearchQuery] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansSearch') || '';
  });
  const [selectedCountry, setSelectedCountry] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansCountry') || 'all';
  });
  const [selectedRegion, setSelectedRegion] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansRegion') || 'all';
  });
  const [selectedStatus, setSelectedStatus] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansStatus') || 'all';
  });
  const [selectedProviderType, setSelectedProviderType] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansProviderType') || 'all';
  });
  const [sortBy, setSortBy] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansSort') || 'createdAt';
  });
  const [currentPage, setCurrentPage] = useState(() => {
    const savedPage = sessionStorage.getItem('adminEsimPlansPage');
    return savedPage ? parseInt(savedPage) : 1;
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [countries, setCountries] = useState([]);
  const [regions, setRegions] = useState([]);
  const [providers, setProviders] = useState([]);
  const [editingPrice, setEditingPrice] = useState(null);
  const [newPrice, setNewPrice] = useState('');
  const [updatingPrice, setUpdatingPrice] = useState(false);
  const [currentTab, setCurrentTab] = useState(() => {
    return sessionStorage.getItem('adminEsimPlansTab') || 'esim_realtime';
  });
  const [syncing, setSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState({ phase: '', message: '', progress: 0 });
  const [billionConnectSyncing, setBillionConnectSyncing] = useState(false);
  const [billionConnectSyncProgress, setBillionConnectSyncProgress] = useState({ phase: '', message: '', progress: 0 });
  const [uploading, setUploading] = useState(false);

  // WebSocket setup for sync notifications
  useEffect(() => {
    const socket = io(import.meta.env.VITE_API_URL || 'http://localhost:3000', {
      withCredentials: true
    });

    socket.on('connect', () => {
      // console.log('Connected to WebSocket for sync notifications');
    });

    socket.on('sync_progress', (data) => {
      // console.log('Sync progress:', data);
      setSyncProgress(data);
      toast({
        title: 'Sync Progress',
        description: data.message
      });
    });

    socket.on('sync_complete', (data) => {
      // console.log('Sync completed:', data);
      setSyncing(false);
      setSyncProgress({ phase: '', message: '', progress: 0 });

      // Update the last sync timestamp
      localStorage.setItem('lastPlanSync', Date.now().toString());

      toast({
        title: 'Sync Complete',
        description: `External plans synced successfully. Updated: ${data.results?.updated || 0}, Created: ${data.results?.created || 0}`
      });

      // Refresh the plans list
      fetchPlans();
    });

    socket.on('sync_error', (data) => {
      // console.log('Sync error:', data);
      setSyncing(false);
      setSyncProgress({ phase: '', message: '', progress: 0 });

      toast({
        variant: 'destructive',
        title: 'Sync Failed',
        description: data.message || 'Failed to sync external plans'
      });
    });

    // BillionConnect sync events
    socket.on('billionconnect_sync_progress', (data) => {
      setBillionConnectSyncProgress(data);
      toast({
        title: 'BillionConnect Sync Progress',
        description: data.message
      });
    });

    socket.on('billionconnect_sync_complete', (data) => {
      setBillionConnectSyncing(false);
      setBillionConnectSyncProgress({ phase: '', message: '', progress: 0 });

      toast({
        title: 'BillionConnect Sync Complete',
        description: `BillionConnect plans synced successfully. Updated: ${data.results?.updated || 0}, Created: ${data.results?.created || 0}`
      });

      // Refresh the plans list
      fetchPlans();
    });

    socket.on('billionconnect_sync_error', (data) => {
      setBillionConnectSyncing(false);
      setBillionConnectSyncProgress({ phase: '', message: '', progress: 0 });

      toast({
        variant: 'destructive',
        title: 'BillionConnect Sync Failed',
        description: data.message || 'Failed to sync BillionConnect plans'
      });
    });

    socket.on('disconnect', () => {
      // console.log('Disconnected from WebSocket');
    });

    // Cleanup on component unmount
    return () => {
      socket.disconnect();
    };
  }, [toast]);

  // Save state to sessionStorage whenever it changes
  useEffect(() => {
    sessionStorage.setItem('adminEsimPlans', JSON.stringify(plans));
    sessionStorage.setItem('adminEsimPlansSearch', searchQuery);
    sessionStorage.setItem('adminEsimPlansCountry', selectedCountry);
    sessionStorage.setItem('adminEsimPlansRegion', selectedRegion);
    sessionStorage.setItem('adminEsimPlansSort', sortBy);
    sessionStorage.setItem('adminEsimPlansPage', currentPage.toString());
    sessionStorage.setItem('adminEsimPlansTab', currentTab);
    sessionStorage.setItem('adminEsimPlansStatus', selectedStatus);
    sessionStorage.setItem('adminEsimPlansProviderType', selectedProviderType);
  }, [plans, searchQuery, selectedCountry, selectedRegion, sortBy, currentPage, currentTab, selectedStatus, selectedProviderType]);

  // Clear session storage when component unmounts and location changes to a non-view page
  useEffect(() => {
    return () => {
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/admin/esim-plans/')) {
        sessionStorage.removeItem('adminEsimPlans');
        sessionStorage.removeItem('adminEsimPlansSearch');
        sessionStorage.removeItem('adminEsimPlansCountry');
        sessionStorage.removeItem('adminEsimPlansRegion');
        sessionStorage.removeItem('adminEsimPlansSort');
        sessionStorage.removeItem('adminEsimPlansPage');
        sessionStorage.removeItem('adminEsimPlansTab');
        sessionStorage.removeItem('adminEsimPlansStatus');
        sessionStorage.removeItem('adminEsimPlansProviderType');
      }
    };
  }, []);

  const shouldSync = () => {
    const lastSync = localStorage.getItem('lastPlanSync');
    const tenHoursInMs = 10 * 60 * 60 * 1000; // 10 hours in milliseconds
    
    if (!lastSync) return true;
    
    const timeSinceLastSync = Date.now() - parseInt(lastSync);
    return timeSinceLastSync > tenHoursInMs;
  };

  const fetchPlans = useCallback(async (showSuccessToast = false) => {
    try {
      setLoading(true);

      // Prepare filter parameters
      const params = {
          page: currentPage,
          limit: 10,
        search: searchQuery,
          sortBy,
          category: currentTab
      };

      // Only add country filter if a specific country is selected
      if (selectedCountry !== 'all') {
        params.countryId = selectedCountry;
      }

      // Only add region filter if a specific region is selected
      if (selectedRegion !== 'all') {
        params.region = selectedRegion;
      }

      // Add status filter if a specific status is selected
      if (selectedStatus !== 'all') {
        params.status = selectedStatus;
      }

      // Add provider type filter if a specific type is selected
      if (selectedProviderType !== 'all') {
        params.providerType = selectedProviderType;
      }

      const response = await api.get('/api/esim-plans', { params });

      setPlans(response.data.plans);
      setTotalPages(response.data.totalPages);
      setTotalItems(response.data.totalItems);
      setCountries(response.data.countries);
      setRegions(response.data.regions);

      // Fetch providers if not already loaded
      if (providers.length === 0) {
        try {
          const providersRes = await api.get('/api/providers');
          setProviders(providersRes.data.filter(p => p.status === 'active'));
        } catch (providerError) {
          console.error('Error fetching providers:', providerError);
        }
      }

      // Show success toast if explicitly requested (manual refresh)
      if (showSuccessToast) {
        toast({
          title: 'Success',
          description: 'eSIM plans refreshed successfully'
        });
      }
    } catch (error) {
      // console.error('Error fetching plans:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch eSIM plans'
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchQuery, sortBy, currentTab, selectedCountry, selectedRegion, selectedStatus, selectedProviderType, toast]);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleCountryChange = (value) => {
    setSelectedCountry(value);
    setCurrentPage(1);
    // Save to session storage immediately
    sessionStorage.setItem('adminEsimPlansCountry', value);
    // Reset region when country changes
    if (value !== 'all') {
      setSelectedRegion('all');
      sessionStorage.setItem('adminEsimPlansRegion', 'all');
    }
  };

  const handleRegionChange = (value) => {
    setSelectedRegion(value);
    setCurrentPage(1);
    // Save to session storage immediately
    sessionStorage.setItem('adminEsimPlansRegion', value);
  };

  const handleStatusChange = (value) => {
    setSelectedStatus(value);
    setCurrentPage(1);
    // Save to session storage immediately
    sessionStorage.setItem('adminEsimPlansStatus', value);
  };

  const handleProviderTypeChange = (value) => {
    setSelectedProviderType(value);
    setCurrentPage(1);
    // Save to session storage immediately
    sessionStorage.setItem('adminEsimPlansProviderType', value);
  };

  const handleSortChange = (value) => {
    setSortBy(value);
    setCurrentPage(1);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // Update the useEffect to handle debounced fetching
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchPlans();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, currentPage, selectedCountry, selectedRegion, sortBy, currentTab, selectedStatus, selectedProviderType]);

  const togglePlanStatus = async (planId, currentStatus) => {
    try {
      const newStatus = currentStatus === 'visible' ? 'hidden' : 'visible';
      await api.patch(`/api/esim-plans/${planId}/status`, {
        status: newStatus
      });
      toast({
        title: 'Success',
        description: `Plan ${newStatus === 'visible' ? 'shown' : 'hidden'} successfully`
      });
      fetchPlans();
    } catch (err) {
      // console.error('Error updating plan status:', err);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.response?.data?.message || 'Failed to update plan status'
      });
    }
  };

  const startEditingPrice = (plan) => {
    setEditingPrice(plan.id);
    setNewPrice(plan.sellingPrice?.toString() || '');
  };

  const handlePriceKeyPress = (e, planId) => {
    if (e.key === 'Enter') {
      handlePriceChange(planId);
    } else if (e.key === 'Escape') {
      setEditingPrice(null);
    }
  };

  const handlePriceChange = async (planId) => {
    if (!newPrice || isNaN(newPrice) || Number(newPrice) <= 0) {
      toast({
        title: 'Invalid Price',
        description: 'Please enter a valid price greater than 0',
        variant: 'destructive',
      });
      return;
    }

    try {
      setUpdatingPrice(true);
      await api.patch(`/api/esim-plans/${planId}/price`, {
        sellingPrice: Number(newPrice)
      });

      setPlans(plans.map(plan => 
        plan.id === planId 
          ? { ...plan, sellingPrice: Number(newPrice) }
          : plan
      ));

      toast({
        title: 'Price Updated',
        description: 'The selling price has been updated successfully.',
      });
    } catch (error) {
      // console.error('Error updating price:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update price',
        variant: 'destructive',
      });
    } finally {
      setUpdatingPrice(false);
      setEditingPrice(null);
    }
  };

  const handlePriceReset = async (planId) => {
    try {
      setUpdatingPrice(true);
      await api.patch(`/api/esim-plans/${planId}/reset-price`);

      setPlans(plans.map(plan => 
        plan.id === planId 
          ? { ...plan, sellingPrice: null }
          : plan
      ));

      toast({
        title: 'Price Reset',
        description: 'The selling price has been reset successfully.',
      });
    } catch (error) {
      // console.error('Error resetting price:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to reset price',
        variant: 'destructive',
      });
    } finally {
      setUpdatingPrice(false);
    }
  };

  const handleDelete = async (Id) => {
    if (!window.confirm('Are you sure you want to delete this eSIM plan?')) {
        return;
    }

    try {
        await api.delete(`/api/esim-plans/${Id}`);
        toast({
            title: "Success",
            description: "eSIM plan deleted successfully"
        });
        fetchPlans();
    } catch (error) {
        toast({
            title: "Error",
            description: error.response?.data?.message || "Failed to delete esim plan",
            variant: "destructive"
        });
    }

};

  const handleSync = async () => {
    try {
      setSyncing(true);

      // Start the sync operation (returns immediately)
      await api.post('/api/esim-plans/sync');

      toast({
        title: 'Sync Started',
        description: 'External plans sync operation started in background. You will be notified when complete.'
      });

      // Note: We don't set syncing to false here because we want to wait for WebSocket notifications
      // The syncing state will be reset when we receive sync_complete or sync_error events

    } catch (error) {
      // console.error('Error starting sync:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to start sync operation'
      });
      setSyncing(false);
    }
  };

  const handleBillionConnectSync = async () => {
    try {
      setBillionConnectSyncing(true);

      // Start the BillionConnect sync operation (returns immediately)
      await api.post('/api/esim-plans/sync/billionconnect');

      toast({
        title: 'BillionConnect Sync Started',
        description: 'BillionConnect plans sync operation started in background. You will be notified when complete.'
      });

      // Note: We don't set syncing to false here because we want to wait for WebSocket notifications
      // The syncing state will be reset when we receive billionconnect_sync_complete or billionconnect_sync_error events

    } catch (error) {
      // console.error('Error starting BillionConnect sync:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to start BillionConnect sync operation'
      });
      setBillionConnectSyncing(false);
    }
  };

  const handleExport = async (category) => {
    try {
      const response = await api.get(`/api/esim-plans/export?category=${category}`, {
        responseType: 'blob'
      });
      
      // Create a blob URL and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `esim-plans-${category}-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Success',
        description: 'Plans exported successfully'
      });
    } catch (error) {
      // console.error('Error exporting plans:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to export plans'
      });
    }
  };

  // Function to format the last sync time
  const getLastSyncTime = () => {
    const lastSync = localStorage.getItem('lastPlanSync');
    if (!lastSync) return 'Never';

    const lastSyncDate = new Date(parseInt(lastSync));
    return lastSyncDate.toLocaleString();
  };

  // Download Excel template for bulk plan upload
  const handleDownloadTemplate = async () => {
    const template = [
      [
        'Name*',
        'Description',
        'Provider Name*',
        'Network Name*',
        'Network Type',
        'Region',
        'Buying Price*',
        'Selling Price',
        'Validity Days*',
        'Plan Type*',
        'Plan Data',
        'Plan Data Unit',
        'Custom Plan Data',
        'Category*',
        'Plan Category*',
        'Voice Available',
        'Voice Minutes',
        'Voice Unit',
        'SMS Available',
        'SMS Count',
        'Top Up Available',
        'Hotspot Available',
        'Activation Policy*',
        'Speed*',
        'Stock Threshold',
        'Countries*',
        'Instructions',
        'Plan Info',
        'Additional Info'
      ],
      [
        'Example Plan',
        'Sample description',
        'Custom Provider',
        'Custom Telecom',
        '4G/LTE',
        'Europe',
        '5.00',
        '10.00',
        '7',
        'Fixed',
        '1',
        'GB',
        null,
        'esim_realtime',
        'Data Only',
        'Not Available',
        null,
        null,
        'Not Available',
        null,
        'Not Available',
        'Available',
        'Activation upon purchase',
        'Unrestricted',
        '10',
        'United States,Canada,United Kingdom',
        'Setup instructions',
        'Plan information',
        'Additional details'
      ]
    ];

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(template);

    // Set column widths
    const colWidths = [
      { wch: 20 }, // Name
      { wch: 30 }, // Description
      { wch: 15 }, // Provider Name
      { wch: 15 }, // Network Name
      { wch: 12 }, // Network Type
      { wch: 15 }, // Region
      { wch: 12 }, // Buying Price
      { wch: 12 }, // Selling Price
      { wch: 12 }, // Validity Days
      { wch: 12 }, // Plan Type
      { wch: 10 }, // Plan Data
      { wch: 12 }, // Plan Data Unit
      { wch: 20 }, // Custom Plan Data
      { wch: 15 }, // Category
      { wch: 15 }, // Plan Category
      { wch: 15 }, // Voice Available
      { wch: 12 }, // Voice Minutes
      { wch: 10 }, // Voice Unit
      { wch: 15 }, // SMS Available
      { wch: 10 }, // SMS Count
      { wch: 15 }, // Top Up Available
      { wch: 15 }, // Hotspot Available
      { wch: 20 }, // Activation Policy
      { wch: 12 }, // Speed
      { wch: 15 }, // Stock Threshold
      { wch: 20 }, // Countries
      { wch: 30 }, // Instructions
      { wch: 30 }, // Plan Info
      { wch: 30 }  // Additional Info
    ];
    ws['!cols'] = colWidths;

    // Define dropdown options for various fields
    const dropdownOptions = {
      providerName: providers.map(p => p.name),
      networkType: ['5G/4G/LTE', '4G/LTE', '2G', '3G', '5G'],
      planType: ['Fixed', 'Unlimited', 'Custom'],
      planDataUnit: ['MB', 'GB', 'TB'],
      category: ['esim_realtime', 'esim_addon', 'esim_replacement'],
      planCategory: ['Data Only', 'Voice and Data'],
      voiceAvailable: ['Available', 'Not Available'],
      voiceUnit: ['Min', 'Hr', 'Sec'],
      smsAvailable: ['Available', 'Not Available'],
      topUpAvailable: ['Available', 'Not Available'],
      hotspotAvailable: ['Available', 'Not Available'],
      activationPolicy: ['Activation upon purchase', 'Activation upon first usage', 'Activation upon travel date'],
      speed: ['Restricted', 'Unrestricted']
    };

    // Add data validation for dropdown columns
    const validations = [];

    // Provider Name (column C) - Updated range for 200 plans
    if (dropdownOptions.providerName.length > 0) {
      validations.push({
        sqref: 'C2:C201',
        type: 'list',
        formula1: `"${dropdownOptions.providerName.join(',')}"`
      });
    }

    // Network Type (column E)
    validations.push({
      sqref: 'E2:E201',
      type: 'list',
      formula1: `"${dropdownOptions.networkType.join(',')}"`
    });

    // Plan Type (column J)
    validations.push({
      sqref: 'J2:J201',
      type: 'list',
      formula1: `"${dropdownOptions.planType.join(',')}"`
    });

    // Plan Data Unit (column L)
    validations.push({
      sqref: 'L2:L201',
      type: 'list',
      formula1: `"${dropdownOptions.planDataUnit.join(',')}"`
    });

    // Category (column N)
    validations.push({
      sqref: 'N2:N201',
      type: 'list',
      formula1: `"${dropdownOptions.category.join(',')}"`
    });

    // Plan Category (column O)
    validations.push({
      sqref: 'O2:O201',
      type: 'list',
      formula1: `"${dropdownOptions.planCategory.join(',')}"`
    });

    // Voice Available (column P)
    validations.push({
      sqref: 'P2:P201',
      type: 'list',
      formula1: `"${dropdownOptions.voiceAvailable.join(',')}"`
    });

    // Voice Unit (column R)
    validations.push({
      sqref: 'R2:R201',
      type: 'list',
      formula1: `"${dropdownOptions.voiceUnit.join(',')}"`
    });

    // SMS Available (column S)
    validations.push({
      sqref: 'S2:S201',
      type: 'list',
      formula1: `"${dropdownOptions.smsAvailable.join(',')}"`
    });

    // Top Up Available (column U)
    validations.push({
      sqref: 'U2:U201',
      type: 'list',
      formula1: `"${dropdownOptions.topUpAvailable.join(',')}"`
    });

    // Hotspot Available (column V)
    validations.push({
      sqref: 'V2:V201',
      type: 'list',
      formula1: `"${dropdownOptions.hotspotAvailable.join(',')}"`
    });

    // Activation Policy (column W)
    validations.push({
      sqref: 'W2:W201',
      type: 'list',
      formula1: `"${dropdownOptions.activationPolicy.join(',')}"`
    });

    // Speed (column X)
    validations.push({
      sqref: 'X2:X201',
      type: 'list',
      formula1: `"${dropdownOptions.speed.join(',')}"`
    });

    // Add data validation to worksheet
    ws['!dataValidation'] = validations;

    XLSX.utils.book_append_sheet(wb, ws, 'Template');

    // Add a second sheet with available countries for reference
    if (countries && countries.length > 0) {
      const countryData = [
        ['Available Countries'],
        ['Country Name'],
        ...countries.map(country => [country.name])
      ];

      const countryWs = XLSX.utils.aoa_to_sheet(countryData);
      countryWs['!cols'] = [{ wch: 30 }]; // Set column width
      XLSX.utils.book_append_sheet(wb, countryWs, 'Available Countries');
    }

    // Add a sheet with available providers for reference
    if (providers && providers.length > 0) {
      const providerData = [
        ['Available Providers'],
        ['Provider Name'],
        ...providers.map(provider => [provider.name])
      ];

      const providerWs = XLSX.utils.aoa_to_sheet(providerData);
      providerWs['!cols'] = [{ wch: 30 }]; // Set column width
      XLSX.utils.book_append_sheet(wb, providerWs, 'Available Providers');
    }

    // Add a third sheet with dropdown options for reference
    const optionsData = [
      ['Field', 'Available Options'],
      ['Provider Name', dropdownOptions.providerName.join(', ')],
      ['Network Type', dropdownOptions.networkType.join(', ')],
      ['Plan Type', dropdownOptions.planType.join(', ')],
      ['Plan Data Unit', dropdownOptions.planDataUnit.join(', ')],
      ['Category', dropdownOptions.category.join(', ')],
      ['Plan Category', dropdownOptions.planCategory.join(', ')],
      ['Voice Available', dropdownOptions.voiceAvailable.join(', ')],
      ['Voice Unit', dropdownOptions.voiceUnit.join(', ')],
      ['SMS Available', dropdownOptions.smsAvailable.join(', ')],
      ['Top Up Available', dropdownOptions.topUpAvailable.join(', ')],
      ['Hotspot Available', dropdownOptions.hotspotAvailable.join(', ')],
      ['Activation Policy', dropdownOptions.activationPolicy.join(', ')],
      ['Speed', dropdownOptions.speed.join(', ')]
    ];

    const optionsWs = XLSX.utils.aoa_to_sheet(optionsData);
    optionsWs['!cols'] = [{ wch: 20 }, { wch: 60 }]; // Set column widths
    XLSX.utils.book_append_sheet(wb, optionsWs, 'Dropdown Options');

    XLSX.writeFile(wb, 'esim_plans_template.xlsx');

    // Download the Bulk Upload Instructions PDF
    try {
      const response = await api.get('/api/esim-plans/download/bulk-upload-instructions', {
        responseType: 'blob'
      });

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'Bulk Upload Instructions.pdf');
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF instructions:', error);
      // Fallback to direct link if API fails
      try {
        const link = document.createElement('a');
        link.href = '/Bulk Upload Instructions.pdf';
        link.setAttribute('download', 'Bulk Upload Instructions.pdf');
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackError) {
        console.error('Fallback PDF download also failed:', fallbackError);
      }
    }

    toast({
      title: 'Files Downloaded',
      description: 'Excel template and instructions PDF downloaded successfully'
    });
  };

  // Handle bulk plan upload from Excel
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setUploading(true);
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const workbook = XLSX.read(e.target.result, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          // Remove header row and empty rows
          const dataRows = jsonData.slice(1).filter(row => row.length > 0);

          if (dataRows.length === 0) {
            toast({
              title: 'No Data',
              description: 'No valid data found in the Excel file',
              variant: 'destructive'
            });
            return;
          }

          // Check if the number of plans exceeds the recommended limit
          if (dataRows.length > 200) {
            toast({
              title: 'Too Many Plans',
              description: `You are trying to upload ${dataRows.length} plans. The maximum recommended limit is 200 plans per upload. Please split your data into smaller batches.`,
              variant: 'destructive'
            });
            return;
          }

          // Map Excel data to plan objects
          const plansData = dataRows.map((row, index) => {
            const [
              name, description, providerName, networkName, networkType, region, buyingPrice, sellingPrice,
              validityDays, planType, planData, planDataUnit, customPlanData, category,
              planCategory, voiceAvailable, voiceMinutes, voiceUnit, smsAvailable, smsCount,
              topUpAvailable, hotspotAvailable, activationPolicy, speed, stockThreshold,
              countries, instructions, planInfo, additionalInfo
            ] = row;

            // Find provider by name
            const provider = providers.find(p =>
              p.name.toLowerCase() === providerName?.toString().trim().toLowerCase()
            );

            return {
              name: name?.toString().trim(),
              description: description?.toString().trim() || null,
              providerId: provider?.id || null,
              providerName: providerName?.toString().trim(),
              networkName: networkName?.toString().trim(),
              networkType: networkType?.toString().trim() || '4G/LTE',
              region: region?.toString().trim() || null,
              buyingPrice: parseFloat(buyingPrice) || 0,
              sellingPrice: sellingPrice ? parseFloat(sellingPrice) : null,
              validityDays: parseInt(validityDays) || 0,
              planType: planType?.toString().trim() || 'Fixed',
              planData: planData ? parseFloat(planData) : null,
              planDataUnit: planDataUnit?.toString().trim() || null,
              customPlanData: customPlanData?.toString().trim() || null,
              category: category?.toString().trim() || 'esim_realtime',
              planCategory: planCategory?.toString().trim() || 'Data Only',
              is_voice: voiceAvailable?.toString().trim() || 'Not Available',
              voiceMin: voiceMinutes ? parseInt(voiceMinutes) : null,
              voiceMinUnit: voiceUnit?.toString().trim() || null,
              is_sms: smsAvailable?.toString().trim() || 'Not Available',
              sms: smsCount ? parseInt(smsCount) : null,
              top_up: topUpAvailable?.toString().trim() || 'Not Available',
              hotspot: hotspotAvailable?.toString().trim() || 'Available',
              activationPolicy: activationPolicy?.toString().trim() || 'Activation upon purchase',
              speed: speed?.toString().trim() || 'Unrestricted',
              stockThreshold: parseInt(stockThreshold) || 10,
              countryNames: countries?.toString().trim().split(',').map(c => c.trim()).filter(c => c) || [],
              instructions: instructions?.toString().trim() || null,
              planInfo: planInfo?.toString().trim() || null,
              additionalInfo: additionalInfo?.toString().trim() || null,
              rowIndex: index + 2 // +2 because we removed header and Excel is 1-indexed
            };
          });

          // Convert country names to country IDs
          const plansWithCountryIds = plansData.map(plan => {
            if (plan.countryNames && plan.countryNames.length > 0) {
              // Find matching countries by name (case-insensitive)
              const matchedCountries = countries.filter(country =>
                plan.countryNames.some(name =>
                  country.name.toLowerCase() === name.toLowerCase()
                )
              );

              // Extract country IDs
              const countryIds = matchedCountries.map(country => country.id);

              // Store both for validation
              return {
                ...plan,
                countries: countryIds,
                originalCountryNames: plan.countryNames,
                matchedCountries: matchedCountries.length
              };
            }
            return { ...plan, countries: [], originalCountryNames: [], matchedCountries: 0 };
          });

          // Validate required fields
          const invalidPlans = plansWithCountryIds.filter(plan =>
            !plan.name || !plan.networkName || !plan.buyingPrice || !plan.validityDays ||
            !plan.countries.length || !plan.category || !plan.planCategory || !plan.providerId
          );

          if (invalidPlans.length > 0) {
            const errorRows = invalidPlans.map(plan => plan.rowIndex).join(', ');
            toast({
              title: 'Validation Error',
              description: `Missing required fields in rows: ${errorRows}. Please check Name, Provider Name, Network Name, Buying Price, Validity Days, Countries, Category, and Plan Category.`,
              variant: 'destructive'
            });
            return;
          }

          // Check for invalid provider names
          const plansWithInvalidProviders = plansWithCountryIds.filter(plan =>
            plan.providerName && !plan.providerId
          );

          if (plansWithInvalidProviders.length > 0) {
            const providerErrors = plansWithInvalidProviders.map(plan =>
              `Row ${plan.rowIndex}: "${plan.providerName}"`
            ).join('; ');

            toast({
              title: 'Invalid Provider Names',
              description: `The following provider names could not be found: ${providerErrors}. Please use exact provider names from the dropdown options.`,
              variant: 'destructive'
            });
            return;
          }

          // Check for unmatched country names
          const plansWithUnmatchedCountries = plansWithCountryIds.filter(plan =>
            plan.originalCountryNames.length > 0 && plan.matchedCountries < plan.originalCountryNames.length
          );

          if (plansWithUnmatchedCountries.length > 0) {
            const countryErrors = plansWithUnmatchedCountries.map(plan => {
              const unmatchedNames = plan.originalCountryNames.filter(name =>
                !countries.some(country => country.name.toLowerCase() === name.toLowerCase())
              );
              return `Row ${plan.rowIndex}: ${unmatchedNames.join(', ')}`;
            }).join('; ');

            toast({
              title: 'Invalid Country Names',
              description: `The following country names could not be found: ${countryErrors}. Please check the spelling and use exact country names.`,
              variant: 'destructive'
            });
            return;
          }

          // Upload data to server (use processed data with country IDs)
          const response = await api.post('/api/esim-plans/bulk', plansWithCountryIds);

          if (response.data && response.data.plans) {
            toast({
              title: 'Success',
              description: `Successfully uploaded ${response.data.plans.length} eSIM plans`
            });
            fetchPlans(); // Refresh the plans list
          } else {
            throw new Error('Invalid response from server');
          }
        } catch (error) {
          console.error('Error processing file:', error);
          toast({
            title: 'Error',
            description: error.response?.data?.message || 'Failed to process Excel file. Please make sure it matches the template format.',
            variant: 'destructive'
          });
        }
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error('Error reading file:', error);
      toast({
        title: 'Error',
        description: 'Failed to read the Excel file',
        variant: 'destructive'
      });
    } finally {
      setUploading(false);
      // Reset file input
      event.target.value = '';
    }
  };

  return (
    <div className="h-full flex flex-col gap-6 p-4 md:p-6 max-w-full overflow-hidden">
      <Tabs
        value={currentTab}
        className="w-full"
        onValueChange={(value) => {
          const isNavigatingBack =
            sessionStorage.getItem("adminEsimPlansTab") === value;
          setCurrentTab(value);
          if (!isNavigatingBack) {
            // Only reset filters if not navigating back
            setCurrentPage(1);
            setSearchQuery("");
            setSelectedCountry("all");
            setSelectedRegion("all");
            setSortBy("createdAt");
            setSelectedStatus("all");
          }
        }}
      >
        <div className="flex flex-col lg:flex-row lg:items-center justify-between w-full gap-4">
          <TabsList className="flex w-full lg:w-auto flex-wrap overflow-x-auto h-full">
            <TabsTrigger value="esim_realtime">eSIM Plans</TabsTrigger>
            <TabsTrigger value="esim_addon">Top-up Plans</TabsTrigger>
            <TabsTrigger value="esim_replacement">
              Replacement Plans
            </TabsTrigger>
          </TabsList>

          {/* Add refresh, sync and Add Plan buttons */}
          <div className="flex gap-2 flex-col sm:flex-row flex-wrap">
            <Button
              onClick={() => fetchPlans(true)}
              variant="outline"
              className="flex items-center gap-2 bg-blue-700 text-white hover:bg-blue-700"
              disabled={loading}
            >
              <RefreshCw
                className={`w-4 h-4 ${loading ? "animate-spin" : ""}`}
              />
              {loading ? "Refreshing..." : "Refresh"}
            </Button>
            <div className="flex flex-col">
              <Button
                onClick={handleSync}
                variant="outline"
                className="flex items-center gap-2 bg-blue-700 text-white"
                disabled={syncing}
              >
                <RefreshCw
                  className={`w-4 h-4 ${syncing ? "animate-spin" : ""}`}
                />
                {syncing ? "Syncing..." : "Sync Mobimatter Plans"}
              </Button>
              {syncing && syncProgress.message && (
                <div className="mt-2 text-xs text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${syncProgress.progress}%` }}
                      ></div>
                    </div>
                    <span>{Math.round(syncProgress.progress)}%</span>
                  </div>
                  <p className="mt-1">{syncProgress.message}</p>
                </div>
              )}
            </div>
            <div className="flex flex-col">
              <Button
                onClick={handleBillionConnectSync}
                variant="outline"
                className="flex items-center gap-2 bg-purple-700 text-white hover:bg-purple-800"
                disabled={billionConnectSyncing}
              >
                <RefreshCw
                  className={`w-4 h-4 ${billionConnectSyncing ? "animate-spin" : ""}`}
                />
                {billionConnectSyncing ? "Syncing..." : "Sync BillionConnect Plans"}
              </Button>
              {billionConnectSyncing && billionConnectSyncProgress.message && (
                <div className="mt-2 text-xs text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-purple-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${billionConnectSyncProgress.progress || 0}%` }}
                      ></div>
                    </div>
                    <span>{billionConnectSyncProgress.progress || 0}%</span>
                  </div>
                  <div className="mt-1">{billionConnectSyncProgress.message}</div>
                </div>
              )}
            </div>
            <Button
              onClick={() => navigate("/admin/esim-plans/new")}
              className="flex items-center gap-2 md:gap-4 md:text-base bg-blue-700 text-white"
            >
              <Plus className="w-4 h-4 " /> Add Plan
            </Button>
          </div>
        </div>
        <TabsContent value="esim_realtime">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative w-full sm:w-auto">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or product ID..."
                className="pl-8 w-full sm:w-[300px]"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto overflow-x-auto min-w-0">
              <Select
                value={selectedCountry}
                onValueChange={handleCountryChange}
              >
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Countries" />
                </SelectTrigger>
                <SelectContent className="max-h-[400px] overflow-y-auto">
                  <SelectItem value="all">All Countries</SelectItem>
                  {countries.map((country) => (
                    <SelectItem key={country.id} value={country.id}>
                      <div className="flex items-center gap-2">
                        <img
                          src={
                            country.flagUrl ||
                            `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`
                          }
                          alt={`${country.name} flag`}
                          className="w-4 h-3 object-cover rounded-sm"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src =
                              "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png";
                          }}
                        />
                        <span>{country.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedRegion} onValueChange={handleRegionChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent className="max-h-[400px] overflow-y-auto">
                  <SelectItem value="all">All Regions</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region} value={region}>
                      {region}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedStatus} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="visible">Visible</SelectItem>
                  <SelectItem value="hidden">Hidden</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedProviderType} onValueChange={handleProviderTypeChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Provider Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Provider Types</SelectItem>
                  <SelectItem value="Custom">Custom Plans</SelectItem>
                  <SelectItem value="API">API Plans</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue>
                    {sortBy === "createdAt" ? "Latest First" : "Oldest First"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Latest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="mt-2">
            <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 py-10 px-10 rounded-t-lg">
              <div className="flex justify-between items-center">
                <CardTitle className="font-semibold text-white">
                  eSIM Plans ({totalItems})
                </CardTitle>
                <div className="flex gap-2 flex-wrap max-w-full overflow-x-auto">
                  <Button
                    onClick={handleDownloadTemplate}
                    variant="secondary"
                    size="sm"
                    className="bg-purple-500 text-white"
                    disabled={uploading}
                  >
                    <FileSpreadsheet className="w-4 h-4 mr-2" />
                    Template
                  </Button>
                  <div className="relative">
                    <input
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="bulk-upload-esim"
                      disabled={uploading}
                    />
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() =>
                        document.getElementById("bulk-upload-esim").click()
                      }
                      className="bg-purple-500 text-white"
                      disabled={uploading}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      {uploading ? "Uploading..." : "Upload Excel"}
                    </Button>
                  </div>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="bg-purple-500 text-white"
                    onClick={() => handleExport("esim_realtime")}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export Esim Plans
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="border rounded-lg overflow-x-auto">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                          <TableHead>Product ID</TableHead>
                          <TableHead>Name</TableHead>
                          {/* <TableHead>Region</TableHead> */}
                          <TableHead>Validity (Days)</TableHead>
                          <TableHead>Data</TableHead>
                          <TableHead>Stock</TableHead>
                          {/* <TableHead>Network</TableHead> */}
                          <TableHead>Provider Name</TableHead>
                          <TableHead>Buying Price ($)</TableHead>
                          <TableHead>Selling Price ($)</TableHead>
                          {/* <TableHead>Start Date</TableHead> */}
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {plans.map((plan) => (
                          <TableRow key={plan.id}>
                            <TableCell className="text-sm font-medium">
                              {plan.productId}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              {plan.name}
                            </TableCell>
                            {/* <TableCell>{Array.isArray(plan.region) ? plan.region.join(", ") : plan.region || '-'}</TableCell> */}
                            <TableCell className="text-sm font-medium">
                              {plan.validityDays}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              {plan.planType === "Unlimited" || plan.planData === -1 ? (
                                <span className="font-medium text-blue-600">
                                  Unlimited
                                </span>
                              ) : plan.planType === "Custom" ? (
                                <span className="font-medium text-purple-600">
                                  {plan.customPlanData}
                                </span>
                              ) : plan.planData && plan.planDataUnit ? (
                                `${plan.planData} ${plan.planDataUnit}`
                              ) : (
                                "-"
                              )}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              <Badge
                                variant={
                                  plan.provider?.name === "Mobimatter" ||
                                  plan.provider?.name === "BillionConnect" ||
                                  plan.provider?.name === "billionconnect" ||
                                  plan.stockCount === "Unlimited"
                                    ? "success"
                                    : plan.stockCount > 0
                                    ? "success"
                                    : "destructive"
                                }
                                className="font-medium"
                              >
                                {plan.provider?.name === "Mobimatter" ||
                                 plan.provider?.name === "BillionConnect" ||
                                 plan.provider?.name === "billionconnect" ||
                                 plan.stockCount === "Unlimited"
                                  ? "Unlimited"
                                  : plan.stockCount}
                              </Badge>
                            </TableCell>
                            {/* <TableCell>{plan.networkName}</TableCell> */}
                            <TableCell className="text-sm font-medium">
                              {plan.provider?.name || "-"}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              ${plan.buyingPrice}
                            </TableCell>
                            <TableCell className="flex items-center gap-2 text-sm font-medium">
                              {editingPrice === plan.id ? (
                                <Input
                                  type="number"
                                  value={newPrice}
                                  onChange={(e) => setNewPrice(e.target.value)}
                                  onKeyDown={(e) =>
                                    handlePriceKeyPress(e, plan.id)
                                  }
                                  onBlur={() => handlePriceChange(plan.id)}
                                  className="w-24"
                                  step="0.01"
                                  min="0"
                                  autoFocus
                                  disabled={updatingPrice}
                                />
                              ) : (
                                <div className="flex items-center gap-2">
                                  <span>
                                    {plan.sellingPrice ? (
                                      `$${plan.sellingPrice}`
                                    ) : (
                                      <span className="text-gray-500 italic">
                                        Not Set
                                      </span>
                                    )}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => startEditingPrice(plan)}
                                      className="h-8 px-2 text-xs"
                                      disabled={updatingPrice}
                                    >
                                      Edit
                                    </Button>
                                    {plan.sellingPrice && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handlePriceReset(plan.id)
                                        }
                                        className="h-8 px-2 text-xs"
                                        disabled={updatingPrice}
                                      >
                                        Reset
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </TableCell>
                            {/* <TableCell>
                              <Button
                                variant={plan.startDateEnabled ? "destructive" : "default"}
                                size="sm"
                                onClick={() => handleStartDateToggle(plan.id)}
                              >
                                {plan.startDateEnabled ? "Required" : "Not Required"}
                              </Button>
                            </TableCell> */}
                            <TableCell>
                              <Badge
                                variant={
                                  plan.status === "visible"
                                    ? "default"
                                    : "destructive"
                                }
                              >
                                {plan.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(`/admin/esim-plans/${plan.id}`)
                                  }
                                  className="h-8 w-8 p-0 bg-blue-600"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(
                                      `/admin/esim-plans/edit/${plan.id}`
                                    )
                                  }
                                  className={`h-8 w-8 p-0 bg-gray-600 ${
                                    plan.provider?.type === "API"
                                      ? "cursor-not-allowed opacity-50"
                                      : ""
                                  }`}
                                  disabled={plan.provider?.type === "API"}
                                  title={
                                    plan.provider?.type === "API"
                                      ? "Cannot edit external plans"
                                      : "Edit plan"
                                  }
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                {/* <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(plan.id)}
                                  disabled={plan.provider?.type === 'API'}
                                  className={`h-8 w-8 p-0 bg-blue-600 ${plan.provider?.type === 'API' ? 'cursor-not-allowed opacity-50' : ''
                                    }`}
                                  title={plan.provider?.type === 'API' ? 'Cannot delete External plans' : 'Delete plan'}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button> */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant={
                                        plan.status === "visible"
                                          ? "default"
                                          : "destructive"
                                      }
                                      size="sm"
                                    >
                                      {plan.status === "visible"
                                        ? "Hide"
                                        : "Show"}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        {plan.status === "visible"
                                          ? "Hide this plan?"
                                          : "Show this plan?"}
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        {plan.status === "visible"
                                          ? "This plan will no longer be visible to customers."
                                          : "This plan will become visible to customers."}
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>
                                        Cancel
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          togglePlanStatus(plan.id, plan.status)
                                        }
                                      >
                                        Continue
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing page {currentPage} of {totalPages}
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="esim_addon">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative w-full sm:w-auto">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or product ID..."
                className="pl-8 w-full sm:w-[300px]"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto overflow-x-auto min-w-0">
              <Select
                value={selectedCountry}
                onValueChange={handleCountryChange}
              >
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] min-w-[140px]">
                  <SelectValue placeholder="All Countries" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  <SelectItem value="all">All Countries</SelectItem>
                  {countries.map((country) => (
                    <SelectItem key={country.id} value={country.id}>
                      <div className="flex items-center gap-2">
                        <img
                          src={
                            country.flagUrl ||
                            `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`
                          }
                          alt={`${country.name} flag`}
                          className="w-4 h-3 object-cover rounded-sm"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src =
                              "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png";
                          }}
                        />
                        <span>{country.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedRegion} onValueChange={handleRegionChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent className="max-h-[400px] overflow-y-auto">
                  <SelectItem value="all">All Regions</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region} value={region}>
                      {region}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedProviderType} onValueChange={handleProviderTypeChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Provider Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Provider Types</SelectItem>
                  <SelectItem value="Custom">Custom Plans</SelectItem>
                  <SelectItem value="API">API Plans</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue>
                    {sortBy === "createdAt" ? "Latest First" : "Oldest First"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Latest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="mt-2">
            <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 py-10 px-10 rounded-t-lg">
              <div className="flex justify-between items-center">
                <CardTitle className="font-semibold text-white">
                  TopUp Plans ({totalItems})
                </CardTitle>
                <div className="flex gap-2 flex-wrap max-w-full overflow-x-auto">
                  <Button
                    onClick={() => fetchPlans(true)}
                    variant="secondary"
                    size="sm"
                    className="bg-green-600 text-white hover:bg-green-700"
                    disabled={loading}
                  >
                    <RefreshCw
                      className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
                    />
                    {loading ? "Refreshing..." : "Refresh"}
                  </Button>
                  <Button
                    onClick={handleDownloadTemplate}
                    variant="secondary"
                    size="sm"
                    className="bg-purple-500 text-white"
                    disabled={uploading}
                  >
                    <FileSpreadsheet className="w-4 h-4 mr-2" />
                    Template
                  </Button>
                  <div className="relative">
                    <input
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="bulk-upload-topup"
                      disabled={uploading}
                    />
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() =>
                        document.getElementById("bulk-upload-topup").click()
                      }
                      className="bg-purple-500 text-white"
                      disabled={uploading}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      {uploading ? "Uploading..." : "Upload Excel"}
                    </Button>
                  </div>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleExport("esim_addon")}
                    className="bg-purple-500 text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export TopUp Plans
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="border rounded-lg overflow-x-auto">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                          <TableHead>Product ID</TableHead>
                          <TableHead>Name</TableHead>
                          {/* <TableHead>Region</TableHead> */}
                          <TableHead>Validity (Days)</TableHead>
                          <TableHead>Data</TableHead>
                          <TableHead>Stock</TableHead>
                          {/* <TableHead>Network</TableHead> */}
                          <TableHead>Provider Name</TableHead>
                          <TableHead>Buying Price ($)</TableHead>
                          <TableHead>Selling Price ($)</TableHead>
                          {/* <TableHead>Start Date</TableHead> */}
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {plans.map((plan) => (
                          <TableRow key={plan.id}>
                            <TableCell className="text-sm font-medium">
                              {plan.productId}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              {plan.name}
                            </TableCell>
                            {/* <TableCell>{Array.isArray(plan.region) ? plan.region.join(", ") : plan.region || '-'}</TableCell> */}
                            <TableCell className="text-sm font-medium">
                              {plan.validityDays} days
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              {plan.planType === "Unlimited" || plan.planData === -1 ? (
                                <span className="font-medium text-blue-600">
                                  Unlimited
                                </span>
                              ) : plan.planType === "Custom" ? (
                                <span className="font-medium text-purple-600">
                                  {plan.customPlanData}
                                </span>
                              ) : plan.planData && plan.planDataUnit ? (
                                `${plan.planData} ${plan.planDataUnit}`
                              ) : (
                                "-"
                              )}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              <Badge
                                variant={
                                  plan.provider?.name === "Mobimatter" ||
                                  plan.provider?.name === "BillionConnect" ||
                                  plan.provider?.name === "billionconnect" ||
                                  plan.stockCount === "Unlimited"
                                    ? "success"
                                    : plan.stockCount > 0
                                    ? "success"
                                    : "destructive"
                                }
                                className="font-medium"
                              >
                                {plan.provider?.name === "Mobimatter" ||
                                 plan.provider?.name === "BillionConnect" ||
                                 plan.provider?.name === "billionconnect" ||
                                 plan.stockCount === "Unlimited"
                                  ? "Unlimited"
                                  : plan.stockCount}
                              </Badge>
                            </TableCell>
                            {/* <TableCell>{plan.networkName}</TableCell> */}
                            <TableCell className="text-sm font-medium">
                              {plan.provider?.name || "-"}
                            </TableCell>
                            <TableCell className="text-sm font-medium">
                              ${plan.buyingPrice}
                            </TableCell>
                            <TableCell className="flex items-center gap-2 text-sm font-medium">
                              {editingPrice === plan.id ? (
                                <Input
                                  type="number"
                                  value={newPrice}
                                  onChange={(e) => setNewPrice(e.target.value)}
                                  onKeyDown={(e) =>
                                    handlePriceKeyPress(e, plan.id)
                                  }
                                  onBlur={() => handlePriceChange(plan.id)}
                                  className="w-24"
                                  step="0.01"
                                  min="0"
                                  autoFocus
                                  disabled={updatingPrice}
                                />
                              ) : (
                                <div className="flex items-center gap-2">
                                  <span>
                                    {plan.sellingPrice ? (
                                      `$${plan.sellingPrice}`
                                    ) : (
                                      <span className="text-gray-500 italic">
                                        Not Set
                                      </span>
                                    )}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => startEditingPrice(plan)}
                                      className="h-8 px-2 text-xs"
                                      disabled={updatingPrice}
                                    >
                                      Edit
                                    </Button>
                                    {plan.sellingPrice && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handlePriceReset(plan.id)
                                        }
                                        className="h-8 px-2 text-xs"
                                        disabled={updatingPrice}
                                      >
                                        Reset
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </TableCell>
                            {/* <TableCell>
                              <Button
                                variant={plan.startDateEnabled ? "destructive" : "default"}
                                size="sm"
                                onClick={() => handleStartDateToggle(plan.id)}
                              >
                                {plan.startDateEnabled ? "Required" : "Not Required"}
                              </Button>
                            </TableCell> */}
                            <TableCell>
                              <Badge
                                variant={
                                  plan.status === "visible"
                                    ? "default"
                                    : "destructive"
                                }
                              >
                                {plan.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(`/admin/esim-plans/${plan.id}`)
                                  }
                                  className="h-8 w-8 p-0 bg-blue-600"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(
                                      `/admin/esim-plans/edit/${plan.id}`
                                    )
                                  }
                                  className={`h-8 w-8 p-0 bg-blue-600 ${
                                    plan.provider?.type === "API"
                                      ? "cursor-not-allowed opacity-50"
                                      : ""
                                  }`}
                                  disabled={plan.provider?.type === "API"}
                                  title={
                                    plan.provider?.type === "API"
                                      ? "Cannot edit external plans"
                                      : "Edit plan"
                                  }
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                {/* <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(plan.id)}
                                  className="h-8 w-8 p-0 bg-blue-600"
                                >
                                  <Trash className="h-4 w-4" />
                                </Button> */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant={
                                        plan.status === "visible"
                                          ? "default"
                                          : "destructive"
                                      }
                                      size="sm"
                                    >
                                      {plan.status === "visible"
                                        ? "Hide"
                                        : "Show"}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        {plan.status === "visible"
                                          ? "Hide this plan?"
                                          : "Show this plan?"}
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        {plan.status === "visible"
                                          ? "This plan will no longer be visible to customers."
                                          : "This plan will become visible to customers."}
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>
                                        Cancel
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          togglePlanStatus(plan.id, plan.status)
                                        }
                                      >
                                        Continue
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing page {currentPage} of {totalPages}
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="esim_replacement">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative w-full sm:w-auto">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name or product ID..."
                className="pl-8 w-full sm:w-[300px]"
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto overflow-x-auto min-w-0">
              <Select
                value={selectedCountry}
                onValueChange={handleCountryChange}
              >
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] min-w-[140px]">
                  <SelectValue placeholder="All Countries" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  <SelectItem value="all">All Countries</SelectItem>
                  {countries.map((country) => (
                    <SelectItem key={country.id} value={country.id}>
                      <div className="flex items-center gap-2">
                        <img
                          src={
                            country.flagUrl ||
                            `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`
                          }
                          alt={`${country.name} flag`}
                          className="w-4 h-3 object-cover rounded-sm"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src =
                              "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png";
                          }}
                        />
                        <span>{country.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedRegion} onValueChange={handleRegionChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent className="max-h-[400px] overflow-y-auto">
                  <SelectItem value="all">All Regions</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region} value={region}>
                      {region}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedProviderType} onValueChange={handleProviderTypeChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue placeholder="All Provider Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Provider Types</SelectItem>
                  <SelectItem value="Custom">Custom Plans</SelectItem>
                  <SelectItem value="API">API Plans</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-full sm:w-[160px] lg:w-[180px] text-sm font-medium min-w-[140px]">
                  <SelectValue>
                    {sortBy === "createdAt" ? "Latest First" : "Oldest First"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">Latest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="mt-2">
            <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 py-10 px-10 rounded-t-lg">
              <div className="flex justify-between items-center">
                <CardTitle className="font-semibold text-white">
                  Replacement Plans ({totalItems})
                </CardTitle>
                <div className="flex gap-2 flex-wrap max-w-full overflow-x-auto">
                  <Button
                    onClick={() => fetchPlans(true)}
                    variant="secondary"
                    size="sm"
                    className="bg-green-600 text-white hover:bg-green-700"
                    disabled={loading}
                  >
                    <RefreshCw
                      className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
                    />
                    {loading ? "Refreshing..." : "Refresh"}
                  </Button>
                  <Button
                    onClick={handleDownloadTemplate}
                    variant="secondary"
                    size="sm"
                    className="bg-purple-500 text-white"
                    disabled={uploading}
                  >
                    <FileSpreadsheet className="w-4 h-4 mr-2" />
                    Template
                  </Button>
                  <div className="relative">
                    <input
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="bulk-upload-replacement"
                      disabled={uploading}
                    />
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() =>
                        document.getElementById("bulk-upload-replacement").click()
                      }
                      className="bg-purple-500 text-white"
                      disabled={uploading}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      {uploading ? "Uploading..." : "Upload Excel"}
                    </Button>
                  </div>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleExport("esim_replacement")}
                    className="bg-purple-500 text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export Replacement Plans
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="w-6 h-6 animate-spin" />
                </div>
              ) : (
                <>
                  <div className="border rounded-lg overflow-x-auto">
                    <Table className="min-w-full">
                      <TableHeader>
                        <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                          <TableHead>Product ID</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Region</TableHead>
                          <TableHead>Validity (Days)</TableHead>
                          <TableHead>Data</TableHead>
                          <TableHead>Stock</TableHead>
                          {/* <TableHead>Network</TableHead> */}
                          <TableHead>Provider Name</TableHead>
                          <TableHead>Buying Price ($)</TableHead>
                          <TableHead>Selling Price ($)</TableHead>
                          {/* <TableHead>Start Date</TableHead> */}
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {plans.map((plan) => (
                          <TableRow key={plan.id}>
                            <TableCell>{plan.productId}</TableCell>
                            <TableCell>{plan.name}</TableCell>
                            <TableCell>
                              {Array.isArray(plan.region)
                                ? plan.region.join(", ")
                                : plan.region || "-"}
                            </TableCell>
                            <TableCell>{plan.validityDays} days</TableCell>
                            <TableCell>
                              {plan.planType === "Unlimited" || plan.planData === -1 ? (
                                <span className="font-medium text-blue-600">
                                  Unlimited
                                </span>
                              ) : plan.planType === "Custom" ? (
                                <span className="font-medium text-purple-600">
                                  {plan.customPlanData}
                                </span>
                              ) : plan.planData && plan.planDataUnit ? (
                                `${plan.planData} ${plan.planDataUnit}`
                              ) : (
                                "-"
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  plan.provider?.name === "Mobimatter" ||
                                  plan.provider?.name === "BillionConnect" ||
                                  plan.provider?.name === "billionconnect" ||
                                  plan.stockCount === "Unlimited"
                                    ? "success"
                                    : plan.stockCount > 0
                                    ? "success"
                                    : "destructive"
                                }
                                className="font-medium"
                              >
                                {plan.provider?.name === "Mobimatter" ||
                                 plan.provider?.name === "BillionConnect" ||
                                 plan.stockCount === "Unlimited"
                                  ? "Unlimited"
                                  : plan.stockCount}
                              </Badge>
                            </TableCell>
                            {/* <TableCell>{plan.networkName}</TableCell> */}
                            <TableCell>{plan.provider?.name || "-"}</TableCell>
                            <TableCell>${plan.buyingPrice}</TableCell>
                            <TableCell className="flex items-center gap-2">
                              {editingPrice === plan.id ? (
                                <Input
                                  type="number"
                                  value={newPrice}
                                  onChange={(e) => setNewPrice(e.target.value)}
                                  onKeyDown={(e) =>
                                    handlePriceKeyPress(e, plan.id)
                                  }
                                  onBlur={() => handlePriceChange(plan.id)}
                                  className="w-24"
                                  step="0.01"
                                  min="0"
                                  autoFocus
                                  disabled={updatingPrice}
                                />
                              ) : (
                                <div className="flex items-center gap-2">
                                  <span>
                                    {plan.sellingPrice ? (
                                      `$${plan.sellingPrice}`
                                    ) : (
                                      <span className="text-gray-500 italic">
                                        Not Set
                                      </span>
                                    )}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => startEditingPrice(plan)}
                                      className="h-8 px-2 text-xs"
                                      disabled={updatingPrice}
                                    >
                                      Edit
                                    </Button>
                                    {plan.sellingPrice && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handlePriceReset(plan.id)
                                        }
                                        className="h-8 px-2 text-xs"
                                        disabled={updatingPrice}
                                      >
                                        Reset
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </TableCell>
                            {/* <TableCell>
                              <Button
                                variant={plan.startDateEnabled ? "destructive" : "default"}
                                size="sm"
                                onClick={() => handleStartDateToggle(plan.id)}
                              >
                                {plan.startDateEnabled ? "Required" : "Not Required"}
                              </Button>
                            </TableCell> */}
                            <TableCell>
                              <Badge
                                variant={
                                  plan.status === "visible"
                                    ? "default"
                                    : "destructive"
                                }
                              >
                                {plan.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(`/admin/esim-plans/${plan.id}`)
                                  }
                                  className="h-8 w-8 p-0 bg-gray-600"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    navigate(
                                      `/admin/esim-plans/edit/${plan.id}`
                                    )
                                  }
                                  className={`h-8 w-8 p-0 bg-gray-600 ${
                                    plan.provider?.type === "API"
                                      ? "cursor-not-allowed opacity-50"
                                      : ""
                                  }`}
                                  disabled={plan.provider?.type === "API"}
                                  title={
                                    plan.provider?.type === "API"
                                      ? "Cannot edit external plans"
                                      : "Edit plan"
                                  }
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                {/* <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(plan.id)}
                                  className="h-8 w-8 p-0 bg-gray-600"
                                >
                                  <Trash className="h-4 w-4" />
                                </Button> */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant={
                                        plan.status === "visible"
                                          ? "default"
                                          : "destructive"
                                      }
                                      size="sm"
                                    >
                                      {plan.status === "visible"
                                        ? "Hide"
                                        : "Show"}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        {plan.status === "visible"
                                          ? "Hide this plan?"
                                          : "Show this plan?"}
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        {plan.status === "visible"
                                          ? "This plan will no longer be visible to customers."
                                          : "This plan will become visible to customers."}
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>
                                        Cancel
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          togglePlanStatus(plan.id, plan.status)
                                        }
                                      >
                                        Continue
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing page {currentPage} of {totalPages}
                    </p>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
