import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"
 
export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

/**
 * Formats a number to display as integer if whole number, or with one decimal place if decimal
 * @param {number|string} value - The number to format
 * @returns {string|null} - Formatted number or null if input is null/undefined
 */
export function formatPlanData(value) {
    if (value === null || value === undefined || value === '') return null;
    const num = Number(value);
    return Number.isInteger(num) ? num.toString() : num.toFixed(1);
}
