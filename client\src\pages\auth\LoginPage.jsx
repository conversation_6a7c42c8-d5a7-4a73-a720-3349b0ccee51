import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from '@/components/ui/dialog';
import { Loader2, Eye, EyeOff } from 'lucide-react';
import axios from '@/lib/axios';

const loginSchema = z.object({
    email: z.string().email('Please enter a valid email'),
    password: z.string().min(6, 'Please enter a valid password'),
});

const otpSchema = z.object({
    otp: z.string().length(6, 'OTP must be 6 digits'),
});

export default function LoginPage() {
    const [isOtpStep, setIsOtpStep] = useState(false);
    const [showOtpInput, setShowOtpInput] = useState(false);
    const [tempToken, setTempToken] = useState('');
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showForgotDialog, setShowForgotDialog] = useState(false);
    const [forgotEmail, setForgotEmail] = useState('');
    const [isForgotLoading, setIsForgotLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const { login, sendOTP, verifyOTP } = useAuth();
    const navigate = useNavigate();
    const { toast } = useToast();

    // Add/remove body class for login page styling
    useEffect(() => {
        document.body.classList.add('login-body');
        // Add pattern overlay div if not present
        let pattern = document.querySelector('.background-pattern');
        if (!pattern) {
            pattern = document.createElement('div');
            pattern.className = 'background-pattern';
            document.body.appendChild(pattern);
        }
        return () => {
            document.body.classList.remove('login-body');
            const pattern = document.querySelector('.background-pattern');
            if (pattern) pattern.remove();
        };
    }, []);

    const loginForm = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    });

    const otpForm = useForm({
        resolver: zodResolver(otpSchema),
        defaultValues: {
            otp: '',
        },
    });

    const onLoginSubmit = async (data) => {
        try {
            setIsLoading(true);
            const result = await login(data.email, data.password);
            setTempToken(result.tempToken);
            setIsOtpStep(true);
            setIsLoading(false);
        } catch (error) {
            setIsLoading(false);
            loginForm.setError('root', { 
                type: 'manual',
                message: error.message || 'Login failed. Please try again.'
            });
        }
    };

    const handleSendOTP = async () => {
        try {
            setIsLoading(true);
            await sendOTP(tempToken);
            setShowOtpInput(true);
            setIsLoading(false);
        } catch (error) {
            setIsLoading(false);
            loginForm.setError('root', {
                type: 'manual',
                message: error.message || 'Failed to send OTP. Please try again.'
            });
        }
    };

    const onOTPSubmit = async (data) => {
        try {
            setIsLoading(true);
            const result = await verifyOTP(data.otp, tempToken);
            setIsLoading(false);
            
            // Navigate based on user role
            if (result.user.role === 'admin') {
                navigate('/admin/dashboard');
            } else {
                navigate('/dashboard');
            }
        } catch (error) {
            setIsLoading(false);
            otpForm.setError('root', {
                type: 'manual',
                message: error.message || 'Invalid OTP. Please try again.'
            });
        }
    };

    const handleForgotPassword = async (e) => {
        e.preventDefault();
        setIsForgotLoading(true);

        try {
            await axios.post('/api/auth/forgot-password', { email: forgotEmail });
            toast({
                title: 'Success',
                description: 'Password reset instructions have been sent to your email'
            });
            setShowForgotDialog(false);
            setForgotEmail('');
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to process request'
            });
        } finally {
            setIsForgotLoading(false);
        }
    };

    return (
        <div className="login-page bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 p-4 animate-gradient-xy relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-400/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-400/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>
            </div>

            <div className="w-full max-w-[420px] mx-auto relative z-10 flex flex-col justify-center min-h-screen">
                {/* Logo and Title */}
                <div className="text-center mb-8 animate-fade-in">
                    <div className="mb-4 flex justify-center">
                        <div className="w-16 h-16 bg-gradient-to-tr from-white/20 to-white/10 backdrop-blur-sm rounded-2xl shadow-lg flex items-center justify-center p-2 border border-white/20">
                            <img src="/favicon.png" alt="eSIM Logo" className="w-full h-full object-contain" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-200">
                        eSIM Platform
                    </h1>
                    <p className="text-sm text-blue-100 mt-2">Enterprise eSIM Management Solution</p>
                </div>

                {/* Main Card */}
                <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8 transition-all duration-300 animate-fade-up hover:shadow-3xl relative before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-blue-500/10 before:to-purple-500/10 before:blur-xl before:-z-10">
                    <div className="text-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">
                            {isOtpStep ? 'Verification Required' : 'Welcome Back'}
                        </h2>
                        <p className="text-sm text-gray-600 mt-1">
                            {isOtpStep 
                                ? 'Click Send OTP to receive a verification code'
                                : 'Sign in to your account to continue'
                            }
                        </p>
                    </div>

                    {!isOtpStep ? (
                        <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-5">
                            <div className="space-y-2">
                                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                                    Email Address
                                </Label>
                                <div className="relative">
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="<EMAIL>"
                                        className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                        {...loginForm.register('email')}
                                    />
                                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                        </svg>
                                    </span>
                                </div>
                                {loginForm.formState.errors.email && (
                                    <p className="text-sm text-red-500 mt-1">
                                        {loginForm.formState.errors.email.message}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                                    Password
                                </Label>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="••••••••"
                                        className="w-full pl-10 pr-12 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                        {...loginForm.register('password')}
                                    />
                                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                        </svg>
                                    </span>
                                    <button
                                        type="button"
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-5 w-5" />
                                        ) : (
                                            <Eye className="h-5 w-5" />
                                        )}
                                    </button>
                                </div>
                                {loginForm.formState.errors.password && (
                                    <p className="text-sm text-red-500 mt-1">
                                        {loginForm.formState.errors.password.message}
                                    </p>
                                )}
                            </div>

                            {loginForm.formState.errors.root && (
                                <div className="p-4 bg-red-50 border border-red-100 rounded-xl">
                                    <p className="text-sm text-red-600">
                                        {loginForm.formState.errors.root.message}
                                    </p>
                                </div>
                            )}

                            <div className="flex items-center ">
                                <div className='text-sm'>
                                    Forgot Password?
                                </div>
                                <div className="text-sm ml-2">
                                    <a
                                        href=""
                                        onClick={(e) => { e.preventDefault(); setShowForgotDialog(true); }}
                                        className="font-medium text-blue-500 border-none outline-none focus:ring-0 focus:border-transparent hover:border-transparent"
                                    >
                                        click here
                                    </a>
                                </div>
                            </div>

                            <div className="flex items-center">
                                <div className="text-sm">
                                    Don't have an account?
                                </div>
                                <div className="text-sm ml-2">
                                    <a
                                        href="https://vizlync.net/conact"
                                        className="font-medium text-blue-500 border-none outline-none focus:ring-0 focus:border-transparent hover:border-transparent"
                                    >
                                       Become a Partner
                                        </a>
                                </div>
                            </div>

                            <Button 
                                type="submit" 
                                className="w-full py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                                disabled={isLoading} 
                            >
                                {isLoading ? (
                                    <div className="flex items-center justify-center">
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Signing in...
                                    </div>
                                ) : (
                                    'Sign in'
                                )}
                            </Button>
                        </form>
                    ) : (
                        <div className="space-y-5">
                            {!showOtpInput ? (
                                <Button 
                                    onClick={handleSendOTP}
                                    className="w-full py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                                    disabled={isLoading}
                                >
                                    {isLoading ? (
                                        <div className="flex items-center justify-center">
                                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Sending OTP...
                                        </div>
                                    ) : (
                                        'Send OTP'
                                    )}
                                </Button>
                            ) : (
                                <form onSubmit={otpForm.handleSubmit(onOTPSubmit)} className="space-y-5">
                                    <div className="space-y-2">
                                        <Label htmlFor="otp" className="text-sm font-medium text-gray-700">
                                            Verification Code
                                        </Label>
                                        <Input
                                            id="otp"
                                            type="text"
                                            maxLength={6}
                                            placeholder="000000"
                                            className="w-full text-center text-2xl tracking-[0.5em] font-mono py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                            {...otpForm.register('otp')}
                                        />
                                        {otpForm.formState.errors.otp && (
                                            <p className="text-sm text-red-500 mt-1">
                                                {otpForm.formState.errors.otp.message}
                                            </p>
                                        )}
                                    </div>

                                    {otpForm.formState.errors.root && (
                                        <div className="p-4 bg-red-50 border border-red-100 rounded-xl">
                                            <p className="text-sm text-red-600">
                                                {otpForm.formState.errors.root.message}
                                            </p>
                                        </div>
                                    )}

                                    <div className="space-y-3">
                                        <Button 
                                            type="submit" 
                                            className="w-full py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                                            disabled={isLoading}
                                        >
                                            {isLoading ? (
                                                <div className="flex items-center justify-center">
                                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Verifying...
                                                </div>
                                            ) : (
                                                'Verify Code'
                                            )}
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            onClick={() => setIsOtpStep(false)}
                                            className="w-full text-sm text-gray-600 hover:text-gray-900 transition-colors py-2"
                                        >
                                            ← Back to Sign In
                                        </Button>
                                    </div>
                                </form>
                            )}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-blue-200 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Protected by enterprise-grade security
                    </p>
                </div>
            </div>

            <Dialog open={showForgotDialog} onOpenChange={setShowForgotDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Reset your password</DialogTitle>
                        <DialogDescription>
                            Enter your email address and we'll send you instructions to reset your password.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleForgotPassword}>
                        <div className="grid gap-4 py-4">
                            <Input
                                id="forgot-email"
                                type="email"
                                placeholder="Email address"
                                value={forgotEmail}
                                onChange={(e) => setForgotEmail(e.target.value)}
                                required
                            />
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setShowForgotDialog(false)}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isForgotLoading}>
                                {isForgotLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Sending...
                                    </>
                                ) : (
                                    'Send Instructions'
                                )}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}
